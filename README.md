# 🏠 Sistema de Control de Portón Inteligente

Sistema automatizado de control de acceso para portones residenciales usando Raspberry Pi, reconocimiento de placas vehiculares y control por relé.

## 📋 Características

- 🚗 **Detección automática de vehículos** en entrada
- 📸 **Captura de imágenes** con timestamp
- 🔍 **Reconocimiento de placas** usando PlateRecognizer API
- ✅ **Verificación de placas autorizadas** en tiempo real
- 🔓 **Apertura automática del portón** para vehículos autorizados
- 🌐 **Dashboard web** para monitoreo y control
- 📊 **Logs detallados** de todos los eventos
- 🎛️ **Control manual** desde interfaz web
- 🔒 **Gestión de placas autorizadas** via web

## 🛠️ Componentes del Sistema

### Hardware Requerido
- **Raspberry Pi 4** (4GB RAM recomendado)
- **Cámara IP** con RTSP (ej: Hikvision, Dahua)
- **Módulo relé** 5V/10A con optoacoplador
- **Sensor magnético** Reed Switch (opcional)
- **Fuente de alimentación** 12V/3A
- **UPS** para respaldo eléctrico
- **Caja protectora** IP65 para exterior

### Software
- **Raspberry Pi OS** (64-bit recomendado)
- **Python 3.9+**
- **OpenCV** para procesamiento de imágenes
- **Flask** para servidor web
- **PlateRecognizer API** para OCR de placas

## 🚀 Instalación

### 1. Preparación del Sistema

```bash
# Actualizar sistema
sudo apt update && sudo apt upgrade -y

# Instalar dependencias del sistema
sudo apt install -y python3-pip python3-venv git
sudo apt install -y python3-gpiozero python3-rpi.gpio python3-lgpio

# Clonar repositorio
git clone <repository-url>
cd gate-control-system
```

### 2. Configuración del Entorno

```bash
# Ejecutar script de instalación automática
chmod +x run_system.sh
./run_system.sh
```

**O instalación manual:**

```bash
# Crear entorno virtual
python3 -m venv venv --system-site-packages
source venv/bin/activate

# Instalar dependencias
pip3 install -r requirements.txt

# Crear directorios
mkdir -p snapshots/entrada snapshots/salida logs
```

### 3. Configuración

#### Configurar API Key
1. Registrarse en [PlateRecognizer](https://platerecognizer.com/)
2. Obtener API Key gratuita (1000 consultas/mes)
3. Editar `config.py`:

```python
API_KEY = "tu_api_key_aqui"
RTSP_CAMERA_ENTRADA = "rtsp://admin:admin@**************:554/11"
```

#### Configurar Cámara IP
```bash
# Probar conexión RTSP
ffplay rtsp://admin:admin@**************:554/11
```

#### Configurar GPIO
```python
# config.py
RELAY_PIN = 17          # GPIO para relé del portón
GATE_SENSOR_PIN = 18    # GPIO para sensor de estado (opcional)
```

## 🔌 Conexiones Hardware

### Relé del Portón
```
Relé -> Raspberry Pi
VCC  -> 5V (Pin 2)
GND  -> GND (Pin 6)
IN   -> GPIO 17 (Pin 11)
```

### Sensor de Estado (Opcional)
```
Reed Switch -> Raspberry Pi
VCC         -> 3.3V (Pin 1)
GND         -> GND (Pin 6)
Signal      -> GPIO 18 (Pin 12)
```

### Diagrama de Conexión
```
[Cámara IP] ──── [Switch/Router] ──── [Raspberry Pi]
                                           │
                                      [Módulo Relé]
                                           │
                                    [Motor del Portón]
```

## 🏃‍♂️ Ejecución

### Inicio Automático
```bash
# Ejecutar sistema completo
python3 run_system.py
```

### Servicios Separados
```bash
# Solo sistema de portón
python3 gate_control_system.py

# Solo servidor web
python3 web_server.py
```

### Configurar Servicio Systemd
```bash
# Crear servicio
sudo tee /etc/systemd/system/gate-system.service << EOF
[Unit]
Description=Gate Control System
After=network.target

[Service]
Type=simple
User=pi
WorkingDirectory=/home/<USER>/gate-control-system
ExecStart=/home/<USER>/gate-control-system/venv/bin/python run_system.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# Habilitar servicio
sudo systemctl enable gate-system.service
sudo systemctl start gate-system.service
```

## 🌐 Dashboard Web

Acceder al dashboard en: `http://*************00:8080`

### Funcionalidades
- 📊 **Estadísticas en tiempo real**
- 📋 **Logs de eventos** con filtros
- 🚗 **Gestión de placas autorizadas**
- 🎛️ **Control manual del portón**
- 🖼️ **Visualización de imágenes** capturadas

### API Endpoints
```
GET  /api/stats          # Estadísticas del sistema
GET  /api/logs           # Logs de eventos
GET  /api/plates         # Placas autorizadas
POST /api/plates         # Agregar placa
DELETE /api/plates/<id>  # Eliminar placa
POST /api/open-gate      # Apertura manual
GET  /images/<filename>  # Servir imágenes
```

## 📁 Estructura del Proyecto

```
gate-control-system/
├── config.py                      # Configuración principal
├── run_system.py                  # Punto de entrada principal
├── gate_control_system.py         # Lógica principal del sistema
├── web_server.py                  # Servidor web Flask
├── requirements.txt               # Dependencias Python
├── run_system.sh                  # Script de instalación
├── authorized_license_plates.json # Base de datos de placas
├── utils/
│   ├── camera.py                  # Manejo de cámara y OCR
│   ├── relay_module.py            # Control del relé
│   ├── vehicle_detector.py        # Detección de vehículos
│   ├── logger.py                  # Sistema de logging
│   └── net.py                     # Utilidades de red
├── templates/
│   └── dashboard.html             # Interfaz web
├── hardware_tests/
│   ├── relay_test.py              # Prueba del relé
│   ├── camera_test.py             # Prueba de cámara
│   └── gpio_test.py               # Prueba GPIO
├── snapshots/
│   ├── entrada/                   # Imágenes de entrada
│   └── salida/                    # Imágenes de salida
└── logs/                          # Archivos de log
```

## 🔧 Configuración Avanzada

### Optimización para Producción
```bash
# Deshabilitar servicios innecesarios
sudo systemctl disable bluetooth
sudo systemctl disable wifi-powersave

# Configurar memoria GPU
echo "gpu_mem=128" | sudo tee -a /boot/config.txt

# Habilitar watchdog
echo "dtparam=watchdog=on" | sudo tee -a /boot/config.txt
```

### Configuración de Red
```bash
# IP estática en /etc/dhcpcd.conf
interface eth0
static ip_address=*************00/24
static routers=*************
static domain_name_servers=*******
```

### Backup Automático
```bash
# Crontab para backup diario
0 2 * * * rsync -av /home/<USER>/gate-control-system/logs/ /backup/logs/
0 3 * * * rsync -av /home/<USER>/gate-control-system/snapshots/ /backup/images/
```

## 🧪 Pruebas

### Pruebas de Hardware
```bash
# Probar relé
python3 hardware_tests/relay_test.py

# Probar cámara
python3 hardware_tests/camera_test.py

# Probar GPIO
python3 hardware_tests/gpio_test.py
```

### Pruebas del Sistema
```bash
# Verificar conectividad
python3 -c "from utils.net import online; print('Online:', online())"

# Probar reconocimiento de placas
python3 -c "from utils.camera import Camera; c=Camera('rtsp://...', 'test'); print(c.recognize_plate('test.jpg'))"
```

## 📊 Monitoreo

### Logs del Sistema
```bash
# Ver logs en tiempo real
tail -f logs/gate_system.log

# Logs por categoría
grep "VEHICLE" logs/gate_system.log
grep "ERROR" logs/gate_system.log
```

### Métricas del Sistema
- **CPU Temperature**: Mantener < 70°C
- **Memory Usage**: Mantener < 80%
- **Disk Usage**: Mantener < 90%
- **Network Latency**: < 100ms a cámara

## 🚨 Solución de Problemas

### Problemas Comunes

#### Cámara no conecta
```bash
# Verificar conectividad
ping **************

# Probar RTSP
ffplay rtsp://admin:admin@**************:554/11
```

#### GPIO no funciona
```bash
# Verificar permisos
sudo usermod -a -G gpio $USER
sudo chmod 666 /dev/gpiomem

# Reiniciar
sudo reboot
```

#### API de placas falla
```bash
# Verificar internet
python3 -c "from utils.net import online; print(online())"

# Verificar API key
curl -H "Authorization: Token YOUR_API_KEY" https://api.platerecognizer.com/v1/
```

### Logs de Diagnóstico
```bash
# Habilitar debug
export LOG_LEVEL=DEBUG
python3 run_system.py

# Verificar servicios
sudo systemctl status gate-system.service
```

## 🔒 Seguridad

### Recomendaciones
- 🔐 **Cambiar contraseñas** por defecto de cámaras
- 🌐 **Usar VPN** para acceso remoto
- 🔄 **Actualizar sistema** regularmente
- 💾 **Backup** de configuración y logs
- 🚨 **Monitoreo** de intentos de acceso

### Cumplimiento Legal
- ⚠️ **Avisos de videovigilancia** visibles
- 📋 **Registro de tratamiento** de datos
- 🗓️ **Retención de logs** según normativa local
- 🔑 **Acceso de emergencia** siempre disponible

## 🤝 Contribución

1. Fork el proyecto
2. Crear rama feature (`git checkout -b feature/nueva-funcionalidad`)
3. Commit cambios (`git commit -am 'Agregar nueva funcionalidad'`)
4. Push a la rama (`git push origin feature/nueva-funcionalidad`)
5. Crear Pull Request

## 📄 Licencia

Este proyecto está bajo la Licencia MIT - ver archivo [LICENSE](LICENSE) para detalles.

## 📞 Soporte

- 📧 **Email**: <EMAIL>
- 📖 **Documentación**: [Wiki del proyecto](wiki-url)
- 🐛 **Issues**: [GitHub Issues](issues-url)
- 💬 **Discusiones**: [GitHub Discussions](discussions-url)

## 🙏 Agradecimientos

- [PlateRecognizer](https://platerecognizer.com/) por la API de OCR
- [OpenCV](https://opencv.org/) por procesamiento de imágenes
- [Raspberry Pi Foundation](https://www.raspberrypi.org/) por el hardware
- Comunidad de desarrolladores IoT

---

**⚠️ Nota**: Este sistema está diseñado para uso residencial. Para instalaciones comerciales, consulte con un profesional en seguridad y cumplimiento normativo.
