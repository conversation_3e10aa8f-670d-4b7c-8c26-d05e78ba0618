#!/bin/bash

# Setup Dynamic DNS for stable domain access
# Works with No-IP, DuckDNS, or Cloudflare

echo "🌐 Configurando Dynamic DNS para acceso estable"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "Seleccione un proveedor de Dynamic DNS:"
echo "1) DuckDNS (Recomendado - Gratis y fácil)"
echo "2) No-IP (Popular - Plan gratuito disponible)"
echo "3) Cloudflare (Profesional - Requiere dominio)"

read -p "Ingrese su opción (1-3): " choice

case $choice in
    1)
        print_status "Configurando DuckDNS..."
        
        read -p "Ingrese su token de DuckDNS: " DUCK_TOKEN
        read -p "Ingrese su subdominio (ej: mi-porton): " DUCK_DOMAIN
        
        # Create DuckDNS update script
        mkdir -p /home/<USER>/duckdns
        cat > /home/<USER>/duckdns/duck.sh <<EOF
#!/bin/bash
echo url="https://www.duckdns.org/update?domains=$DUCK_DOMAIN&token=$DUCK_TOKEN&ip=" | curl -k -o /home/<USER>/duckdns/duck.log -K -
EOF
        
        chmod 700 /home/<USER>/duckdns/duck.sh
        chown -R $SUDO_USER:$SUDO_USER /home/<USER>/duckdns
        
        # Add to crontab
        (crontab -u $SUDO_USER -l 2>/dev/null; echo "*/5 * * * * /home/<USER>/duckdns/duck.sh >/dev/null 2>&1") | crontab -u $SUDO_USER -
        
        # Test the script
        sudo -u $SUDO_USER /home/<USER>/duckdns/duck.sh
        
        print_status "DuckDNS configurado. Su dominio será: $DUCK_DOMAIN.duckdns.org"
        DOMAIN="$DUCK_DOMAIN.duckdns.org"
        ;;
        
    2)
        print_status "Configurando No-IP..."
        
        # Download and install No-IP client
        cd /usr/local/src
        wget -q http://www.no-ip.com/client/linux/noip-duc-linux.tar.gz
        tar xzf noip-duc-linux.tar.gz
        cd noip-2.1.9-1
        make install
        
        print_status "No-IP instalado. Ejecute 'sudo noip2 -C' para configurar"
        
        # Create service
        cat > /etc/systemd/system/noip2.service <<EOF
[Unit]
Description=No-IP Dynamic DNS Update Client
After=network.target

[Service]
Type=forking
ExecStart=/usr/local/bin/noip2
Restart=always

[Install]
WantedBy=multi-user.target
EOF
        
        systemctl enable noip2
        
        read -p "Ingrese su dominio No-IP (ej: mi-porton.ddns.net): " DOMAIN
        ;;
        
    3)
        print_status "Configurando Cloudflare DDNS..."
        
        read -p "Ingrese su email de Cloudflare: " CF_EMAIL
        read -p "Ingrese su API Key de Cloudflare: " CF_API_KEY
        read -p "Ingrese su zona/dominio (ej: ejemplo.com): " CF_ZONE
        read -p "Ingrese el subdominio (ej: porton): " CF_SUBDOMAIN
        
        # Create Cloudflare update script
        cat > /usr/local/bin/cloudflare-ddns.sh <<EOF
#!/bin/bash

# Cloudflare DDNS updater
CF_EMAIL="$CF_EMAIL"
CF_API_KEY="$CF_API_KEY"
CF_ZONE="$CF_ZONE"
CF_RECORD="$CF_SUBDOMAIN"

# Get current public IP
CURRENT_IP=\$(curl -s ifconfig.me)

# Get zone ID
ZONE_ID=\$(curl -s -X GET "https://api.cloudflare.com/client/v4/zones?name=\$CF_ZONE" \\
  -H "X-Auth-Email: \$CF_EMAIL" \\
  -H "X-Auth-Key: \$CF_API_KEY" \\
  -H "Content-Type: application/json" | jq -r '.result[0].id')

# Get record ID
RECORD_ID=\$(curl -s -X GET "https://api.cloudflare.com/client/v4/zones/\$ZONE_ID/dns_records?name=\$CF_RECORD.\$CF_ZONE" \\
  -H "X-Auth-Email: \$CF_EMAIL" \\
  -H "X-Auth-Key: \$CF_API_KEY" \\
  -H "Content-Type: application/json" | jq -r '.result[0].id')

# Update record
curl -s -X PUT "https://api.cloudflare.com/client/v4/zones/\$ZONE_ID/dns_records/\$RECORD_ID" \\
  -H "X-Auth-Email: \$CF_EMAIL" \\
  -H "X-Auth-Key: \$CF_API_KEY" \\
  -H "Content-Type: application/json" \\
  --data '{"type":"A","name":"'\$CF_RECORD'","content":"'\$CURRENT_IP'","ttl":120}'
EOF
        
        chmod +x /usr/local/bin/cloudflare-ddns.sh
        
        # Add to crontab
        (crontab -l 2>/dev/null; echo "*/5 * * * * /usr/local/bin/cloudflare-ddns.sh >/dev/null 2>&1") | crontab -
        
        # Install jq for JSON parsing
        apt install -y jq
        
        DOMAIN="$CF_SUBDOMAIN.$CF_ZONE"
        ;;
        
    *)
        print_error "Opción inválida"
        exit 1
        ;;
esac

print_status "Configurando SSL con Let's Encrypt..."

# Install certbot
apt install -y certbot python3-certbot-nginx

print_status "Configuración DDNS completada!"

echo ""
echo "📋 CONFIGURACIÓN DDNS:"
echo "====================="
echo "🌐 Su dominio: $DOMAIN"
echo "🔒 Para SSL: sudo certbot --nginx -d $DOMAIN"
echo ""
echo "📝 PRÓXIMOS PASOS:"
echo "=================="
echo "1. Configurar port forwarding en router:"
echo "   Puerto 80 → IP local:80"
echo "   Puerto 443 → IP local:443"
echo ""
echo "2. Esperar 5-10 minutos para propagación DNS"
echo ""
echo "3. Configurar SSL:"
echo "   sudo certbot --nginx -d $DOMAIN"
echo ""
echo "4. Acceder a: https://$DOMAIN"
echo ""
print_warning "Verifique que su router soporte port forwarding"
print_warning "Algunos ISPs bloquean puertos 80/443 en planes residenciales"
