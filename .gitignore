# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# PEP 582; used by e.g. github.com/David-OConnor/pyflow
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# Gate Control System specific files
# ===================================

# Configuration files with sensitive data
config_local.py
.env.local

# API Keys and secrets
api_keys.txt
secrets.json

# Captured images and snapshots
snapshots/
*.jpg
*.jpeg
*.png
*.bmp
*.tiff

# Log files
logs/
*.log
*.log.*

# Database files
*.db
*.sqlite
*.sqlite3
# authorized_license_plates.json

# Backup files
backup/
*.backup
*.bak

# Temporary files
temp/
tmp/
*.tmp
*~
.DS_Store
Thumbs.db

# Video files (if any)
*.mp4
*.avi
*.mov
*.mkv

# System files
.directory
desktop.ini

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Hardware test outputs
hardware_tests/test_output/
hardware_tests/*.jpg
hardware_tests/*.png

# SSL certificates
*.pem
*.key
*.crt
*.cert

# Process IDs
*.pid

# Coverage reports
htmlcov/
.coverage

# Local development
local_config.py
dev_settings.py

# Documentation build
docs/build/

# Package files
*.tar.gz
*.zip
*.rar

# Raspberry Pi specific
# Boot config backups
boot_config.backup

# GPIO state files
gpio_state.json

# Network configuration backups
network_backup/

# System monitoring data
monitoring_data/
system_stats.json

# Cache directories
.cache/
cache/

# Lock files
*.lock

# Runtime files
run/
*.sock

# Compiled translations
locale/*/LC_MESSAGES/*.mo

# Local test files
test_images/
test_videos/
test_data/