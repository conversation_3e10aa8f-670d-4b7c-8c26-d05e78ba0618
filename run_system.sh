#!/bin/bash
echo "🚀 Iniciando sistema completo de portón en Raspberry Pi..."

# Verificar que estamos en Raspberry Pi
if ! grep -q "Raspberry Pi" /proc/cpuinfo 2>/dev/null; then
    echo "⚠️  Advertencia: No se detectó Raspberry Pi, continuando..."
fi

# Verificar que Python está instalado
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 no está instalado"
    exit 1
fi

# Instalar dependencias del sistema para GPIO PRIMERO
echo "🔧 Instalando dependencias del sistema..."
sudo apt update
sudo apt install -y python3-gpiozero python3-rpi.gpio python3-lgpio python3-pigpio

# Crear/activar entorno virtual con acceso al sistema
if [ ! -f "venv/bin/activate" ]; then
    echo "📦 Creando entorno virtual con acceso al sistema..."
    python3 -m venv venv --system-site-packages
else
    echo "📦 Activando entorno virtual..."
fi
source venv/bin/activate

# Instalar dependencias desde requirements.txt
echo "📋 Instalando dependencias desde requirements.txt..."
if [ -f "requirements.txt" ]; then
    pip3 install -r requirements.txt
else
    echo "❌ No se encontró requirements.txt"
    exit 1
fi

# Crear directorios necesarios
echo "📁 Creando directorios..."
mkdir -p snapshots/entrada
mkdir -p snapshots/salida
mkdir -p logs

# Verificar y configurar permisos GPIO
if [ -e /dev/gpiomem ]; then
    echo "🔌 Configurando permisos GPIO..."
    sudo usermod -a -G gpio $USER
    sudo chmod 666 /dev/gpiomem
    echo "✅ Permisos GPIO configurados"
fi

# Ejecutar sistema
echo "🚗 Iniciando sistema de portón..."
python3 run_system.py

echo "🛑 Sistema detenido"
