#!/bin/bash

# Setup cloud tunneling for easy remote access
# Options: Cloudflare Tunnel, ngrok, or localtunnel

echo "🌐 Configurando túnel para acceso remoto"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_option() {
    echo -e "${BLUE}[OPTION]${NC} $1"
}

echo "Seleccione una opción de túnel:"
echo "1) Cloudflare Tunnel (Recomendado - Gratis y seguro)"
echo "2) ngrok (Fácil setup - Plan gratuito limitado)"
echo "3) localtunnel (Simple - Menos estable)"

read -p "Ingrese su opción (1-3): " choice

case $choice in
    1)
        print_status "Configurando Cloudflare Tunnel..."
        
        # Download cloudflared
        wget -q https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-linux-arm64.deb
        sudo dpkg -i cloudflared-linux-arm64.deb
        rm cloudflared-linux-arm64.deb
        
        print_status "Cloudflared instalado. Configuración manual requerida:"
        echo ""
        echo "📝 PASOS PARA CLOUDFLARE TUNNEL:"
        echo "==============================="
        echo "1. Crear cuenta en Cloudflare (gratis)"
        echo "2. Agregar un dominio (puede usar un subdominio gratuito)"
        echo "3. Ejecutar: cloudflared tunnel login"
        echo "4. Ejecutar: cloudflared tunnel create gate-control"
        echo "5. Ejecutar: cloudflared tunnel route dns gate-control su-dominio.com"
        echo "6. Crear archivo de configuración:"
        echo ""
        
        cat > ~/.cloudflared/config.yml <<EOF
tunnel: gate-control
credentials-file: ~/.cloudflared/[TUNNEL-ID].json

ingress:
  - hostname: su-dominio.com
    service: http://localhost:8080
    originRequest:
      httpHostHeader: su-dominio.com
  - service: http_status:404
EOF
        
        echo "7. Ejecutar: cloudflared tunnel run gate-control"
        echo ""
        print_warning "Reemplace 'su-dominio.com' con su dominio real"
        ;;
        
    2)
        print_status "Configurando ngrok..."
        
        # Download ngrok
        wget -q https://bin.equinox.io/c/bNyj1mQVY4c/ngrok-v3-stable-linux-arm64.tgz
        tar xzf ngrok-v3-stable-linux-arm64.tgz
        sudo mv ngrok /usr/local/bin/
        rm ngrok-v3-stable-linux-arm64.tgz
        
        print_status "ngrok instalado. Configuración requerida:"
        echo ""
        echo "📝 PASOS PARA NGROK:"
        echo "==================="
        echo "1. Crear cuenta en ngrok.com (gratis)"
        echo "2. Obtener authtoken de su dashboard"
        echo "3. Ejecutar: ngrok config add-authtoken [SU-TOKEN]"
        echo "4. Ejecutar: ngrok http 8080"
        echo ""
        echo "🔗 Su aplicación estará disponible en la URL que muestre ngrok"
        echo ""
        
        # Create ngrok service
        sudo tee /etc/systemd/system/ngrok.service > /dev/null <<EOF
[Unit]
Description=ngrok tunnel
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=/home/<USER>
ExecStart=/usr/local/bin/ngrok http 8080
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF
        
        print_status "Servicio ngrok creado. Para iniciarlo:"
        echo "sudo systemctl enable ngrok"
        echo "sudo systemctl start ngrok"
        ;;
        
    3)
        print_status "Configurando localtunnel..."
        
        # Install Node.js and localtunnel
        curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
        sudo apt-get install -y nodejs
        sudo npm install -g localtunnel
        
        print_status "localtunnel instalado."
        echo ""
        echo "📝 PARA USAR LOCALTUNNEL:"
        echo "========================"
        echo "Ejecutar: lt --port 8080 --subdomain gate-control-$(whoami)"
        echo ""
        echo "🔗 Su aplicación estará disponible en:"
        echo "https://gate-control-$(whoami).loca.lt"
        echo ""
        
        # Create localtunnel service
        sudo tee /etc/systemd/system/localtunnel.service > /dev/null <<EOF
[Unit]
Description=localtunnel
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=/home/<USER>
ExecStart=/usr/bin/lt --port 8080 --subdomain gate-control-$USER
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF
        
        print_status "Servicio localtunnel creado. Para iniciarlo:"
        echo "sudo systemctl enable localtunnel"
        echo "sudo systemctl start localtunnel"
        ;;
        
    *)
        print_error "Opción inválida"
        exit 1
        ;;
esac

echo ""
print_status "Configuración de túnel completada!"
echo ""
echo "⚠️  CONSIDERACIONES DE SEGURIDAD:"
echo "================================="
echo "• Los túneles exponen su aplicación a internet"
echo "• Use autenticación fuerte para funciones críticas"
echo "• Monitoree los logs de acceso regularmente"
echo "• Considere usar VPN para mayor seguridad"
echo ""
echo "🔧 CONFIGURACIÓN ADICIONAL RECOMENDADA:"
echo "======================================"
echo "• Habilitar HTTPS en su aplicación"
echo "• Configurar rate limiting estricto"
echo "• Implementar autenticación de dos factores"
echo "• Restringir acceso a endpoints críticos"
