import logging
import os
from datetime import datetime
from enum import Enum

class LogLevel(Enum):
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

class LogCategory(Enum):
    SYSTEM = "SYSTEM"
    VEHICLE = "VEHICLE"
    GATE = "GATE"
    CAMERA = "CAMERA"
    WEB = "WEB"
    PLATE = "PLATE"

def setup_logger(name="gate_system", log_dir="logs"):
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    logger = logging.getLogger(name)
    logger.setLevel(logging.DEBUG)
    
    # Limpiar handlers existentes
    logger.handlers.clear()
    
    # File handler - Log completo
    log_file = os.path.join(log_dir, f"system_{datetime.now().strftime('%Y%m%d')}.log")
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(logging.DEBUG)
    
    # File handler - Solo errores
    error_file = os.path.join(log_dir, f"errors_{datetime.now().strftime('%Y%m%d')}.log")
    error_handler = logging.FileHandler(error_file, encoding='utf-8')
    error_handler.setLevel(logging.ERROR)
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # Formatters
    detailed_formatter = logging.Formatter(
        '%(asctime)s | %(levelname)-8s | %(name)-12s | %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    console_formatter = logging.Formatter(
        '%(asctime)s | %(levelname)-8s | %(message)s',
        datefmt='%H:%M:%S'
    )
    
    file_handler.setFormatter(detailed_formatter)
    error_handler.setFormatter(detailed_formatter)
    console_handler.setFormatter(console_formatter)
    
    logger.addHandler(file_handler)
    logger.addHandler(error_handler)
    logger.addHandler(console_handler)
    
    return logger

def log_vehicle_event(logger, category: LogCategory, level: LogLevel, message: str, **kwargs):
    """Log estandarizado para eventos de vehículos"""
    extra_info = " | ".join([f"{k}={v}" for k, v in kwargs.items() if v is not None])
    formatted_message = f"[{category.value}] {message}"
    if extra_info:
        formatted_message += f" | {extra_info}"
    
    getattr(logger, level.value.lower())(formatted_message)

def log_gate_event(logger, action: str, duration: int = None, reason: str = None):
    """Log específico para eventos del portón"""
    message = f"[GATE] {action}"
    if duration:
        message += f" | duration={duration}s"
    if reason:
        message += f" | reason={reason}"
    logger.info(message)

def log_system_event(logger, component: str, action: str, status: str = "OK", details: str = None):
    """Log específico para eventos del sistema"""
    message = f"[SYSTEM] {component} | {action} | status={status}"
    if details:
        message += f" | {details}"
    logger.info(message)
