#!/usr/bin/env python3
"""
Security configuration and utilities for Gate Control System
"""

import os
import hashlib
import secrets
import time
import json
from datetime import datetime, timedelta
from functools import wraps
from flask import request, jsonify
import logging

class SecurityManager:
    def __init__(self, config):
        self.config = config
        self.failed_attempts = {}
        self.blocked_ips = set()
        self.session_tokens = {}
        self.logger = logging.getLogger("security")
        
    def generate_api_token(self, user_id="admin"):
        """Generate a secure API token"""
        token_data = {
            "user_id": user_id,
            "created": datetime.now().isoformat(),
            "expires": (datetime.now() + timedelta(days=30)).isoformat()
        }
        
        # Create token
        token_string = f"{user_id}:{int(time.time())}:{secrets.token_hex(16)}"
        token_hash = hashlib.sha256(token_string.encode()).hexdigest()
        
        self.session_tokens[token_hash] = token_data
        return token_hash
    
    def verify_api_token(self, token):
        """Verify API token validity"""
        if not token or token not in self.session_tokens:
            return False
            
        token_data = self.session_tokens[token]
        expires = datetime.fromisoformat(token_data["expires"])
        
        if datetime.now() > expires:
            del self.session_tokens[token]
            return False
            
        return True
    
    def is_ip_blocked(self, ip):
        """Check if IP is blocked due to failed attempts"""
        if ip in self.blocked_ips:
            return True
            
        if ip in self.failed_attempts:
            attempts = self.failed_attempts[ip]
            # Block for 1 hour after 5 failed attempts
            if attempts["count"] >= 5:
                if datetime.now() - datetime.fromisoformat(attempts["last_attempt"]) < timedelta(hours=1):
                    return True
                else:
                    # Reset after block period
                    del self.failed_attempts[ip]
                    
        return False
    
    def record_failed_attempt(self, ip):
        """Record a failed authentication attempt"""
        now = datetime.now().isoformat()
        
        if ip in self.failed_attempts:
            self.failed_attempts[ip]["count"] += 1
            self.failed_attempts[ip]["last_attempt"] = now
        else:
            self.failed_attempts[ip] = {
                "count": 1,
                "first_attempt": now,
                "last_attempt": now
            }
            
        self.logger.warning(f"Failed auth attempt from {ip}. Count: {self.failed_attempts[ip]['count']}")
        
        # Block IP after 5 attempts
        if self.failed_attempts[ip]["count"] >= 5:
            self.blocked_ips.add(ip)
            self.logger.error(f"IP {ip} blocked due to repeated failed attempts")
    
    def is_local_network(self, ip):
        """Check if IP is from local network"""
        local_ranges = [
            "*********/8",
            "10.0.0.0/8", 
            "**********/12",
            "***********/16"
        ]
        
        # Simple check for common local IPs
        return (ip.startswith("127.") or 
                ip.startswith("10.") or
                ip.startswith("192.168.") or
                ip.startswith("172."))
    
    def require_auth(self, f):
        """Decorator for endpoints requiring authentication"""
        @wraps(f)
        def decorated_function(*args, **kwargs):
            client_ip = request.environ.get('HTTP_X_REAL_IP', request.remote_addr)
            
            # Check if IP is blocked
            if self.is_ip_blocked(client_ip):
                self.logger.warning(f"Blocked IP {client_ip} attempted access")
                return jsonify({'error': 'IP bloqueada temporalmente'}), 429
            
            # Check authentication
            auth_header = request.headers.get('Authorization')
            if not auth_header or not auth_header.startswith('Bearer '):
                self.record_failed_attempt(client_ip)
                return jsonify({'error': 'Token de autorización requerido'}), 401
            
            token = auth_header.split(' ')[1]
            if not self.verify_api_token(token):
                self.record_failed_attempt(client_ip)
                return jsonify({'error': 'Token inválido o expirado'}), 401
            
            # Log successful access
            self.logger.info(f"Authenticated access from {client_ip}")
            return f(*args, **kwargs)
            
        return decorated_function
    
    def require_local_access(self, f):
        """Decorator for endpoints requiring local network access"""
        @wraps(f)
        def decorated_function(*args, **kwargs):
            client_ip = request.environ.get('HTTP_X_REAL_IP', request.remote_addr)
            
            if not self.is_local_network(client_ip):
                self.logger.warning(f"Remote IP {client_ip} attempted local-only access")
                return jsonify({'error': 'Acceso solo permitido desde red local'}), 403
            
            return f(*args, **kwargs)
            
        return decorated_function
    
    def log_security_event(self, event_type, details, severity="INFO"):
        """Log security events"""
        event = {
            "timestamp": datetime.now().isoformat(),
            "type": event_type,
            "details": details,
            "severity": severity,
            "ip": request.environ.get('HTTP_X_REAL_IP', request.remote_addr) if request else "system"
        }
        
        # Log to file
        log_file = "logs/security_events.log"
        os.makedirs("logs", exist_ok=True)
        
        with open(log_file, "a") as f:
            f.write(json.dumps(event) + "\n")
        
        # Log to system logger
        getattr(self.logger, severity.lower())(f"[{event_type}] {details}")

def create_security_manager(config):
    """Factory function to create security manager"""
    return SecurityManager(config)

# Security middleware
def setup_security_headers(app):
    """Setup security headers for Flask app"""
    
    @app.after_request
    def add_security_headers(response):
        # Security headers
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'DENY'
        response.headers['X-XSS-Protection'] = '1; mode=block'
        response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
        response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        
        # Remove server header
        response.headers.pop('Server', None)
        
        return response
    
    return app

# Rate limiting configuration
RATE_LIMITS = {
    'default': "100 per hour",
    'auth': "10 per minute", 
    'critical': "3 per minute",
    'api': "50 per hour"
}

# Security configuration
SECURITY_CONFIG = {
    'max_failed_attempts': 5,
    'block_duration_hours': 1,
    'token_expiry_days': 30,
    'require_https': True,
    'allowed_origins': ['localhost', '127.0.0.1'],
    'log_all_requests': True
}
