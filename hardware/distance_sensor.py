import logging
from gpiozero import DistanceSensor as GPIODistanceSensor
from threading import Lock

class DistanceSensor:
    def __init__(self, trigger_pin, echo_pin, max_distance=4.0, threshold_distance=1.0):
        self.trigger_pin = trigger_pin
        self.echo_pin = echo_pin
        self.max_distance = max_distance
        self.threshold_distance = threshold_distance
        self.logger = logging.getLogger("gate_system")
        self._lock = Lock()
        
        try:
            self.sensor = GPIODistanceSensor(
                echo=echo_pin,
                trigger=trigger_pin,
                max_distance=max_distance,
                threshold_distance=threshold_distance
            )
            self.logger.info(f"📏 Sensor JSN-SR04T inicializado (trigger: {trigger_pin}, echo: {echo_pin})")
        except Exception as e:
            self.logger.error(f"❌ Error inicializando sensor: {e}")
            self.sensor = None


    def get_distance(self): 
        if not self.sensor:
            return None
        
        try:
            with self._lock:
                distance = self.sensor.distance
                return round(distance, 2) if distance else None
        except Exception as e:
            self.logger.error(f"❌ Error leyendo distancia: {e}")
            return None


    def object_detected(self): 
        distance = self.get_distance()
        if distance is None:
            return False
        
        detected = distance <= self.threshold_distance
        if detected:
            self.logger.debug(f"📏 Objeto detectado a {distance}m")
        
        return detected


    def cleanup(self): 
        try:
            if self.sensor:
                self.sensor.close()
            self.logger.info("📏 Sensor de distancia cerrado")
        except Exception as e:
            self.logger.error(f"❌ Error cerrando sensor: {e}")