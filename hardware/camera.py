import base64
import requests
import os
import logging
from datetime import datetime
from config import config
import cv2

class Camera:
    def __init__(self, rtsp_url, location):
        self.rtsp_url = rtsp_url
        self.location = location
        self.logger = logging.getLogger("gate_system")
        self.cap = None
        self._init_camera()
    
    
    def _init_camera(self):
        try:
            self.cap = cv2.VideoCapture(self.rtsp_url)
            if not self.cap.isOpened():
                self.logger.error(f"❌ No se pudo conectar a cámara {self.location}")
                self.cap = None
            else:
                self.logger.info(f"📹 Cámara {self.location} conectada")
        except Exception as e:
            self.logger.error(f"❌ Error inicializando cámara {self.location}: {e}")
            self.cap = None
    
    
    def get_frame(self):
        if self.cap is None or not self.cap.isOpened():
            return None
            
        ret, frame = self.cap.read()
        return frame if ret else None
    
    
    def take_snapshot(self, subfolder=""):
        try:
            cap = cv2.VideoCapture(self.rtsp_url)
            if not cap.isOpened():
                self.logger.error(f"No se pudo conectar a cámara {self.location}")
                return None
            
            ret, frame = cap.read()
            cap.release()
            
            if not ret:
                self.logger.error(f"No se pudo capturar imagen de {self.location}")
                return None
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{timestamp}.jpg"
            
            if subfolder:
                directory = os.path.join(config.SNAPSHOTS_DIR, subfolder)
                os.makedirs(directory, exist_ok=True)
                filepath = os.path.join(directory, filename)
            else:
                filepath = os.path.join(config.SNAPSHOTS_DIR, filename)
            
            cv2.imwrite(filepath, frame)
            self.logger.info(f"📸 Imagen capturada: {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"❌ Error capturando imagen: {e}")
            return None
    
    
    def recognize_plate(self, image_path):
        try:
            with open(image_path, "rb") as f:
                b64 = base64.b64encode(f.read()).decode("utf-8")
            
            response = requests.post(
                "https://api.platerecognizer.com/v1/plate-reader/",
                data={"upload": b64},
                headers={"Authorization": f"Token {config.API_KEY}"},
                timeout=10
            )
            response.raise_for_status()
            
            results = response.json()
            if results.get("results"):
                plate = results["results"][0]["plate"].upper()
                self.logger.info(f"🔍 Placa reconocida: {plate}")
                return plate
            else:
                self.logger.warning("⚠️ No se detectaron placas")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ Error reconociendo placa: {e}")
            return None
    
    
    def cleanup(self):
        if self.cap:
            self.cap.release()
        self.logger.info(f"📹 Cámara {self.location} desconectada")
