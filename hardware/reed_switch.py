import logging
from gpiozero import <PERSON><PERSON>
from threading import Lock
import time

class ReedSwitch:
    def __init__(self, pin, active_high=False, pull_up=True, bounce_time=0.1):
        """
        Inicializar sensor Reed Switch para detectar estado del portón
        
        Args:
            pin: Pin GPIO para el reed switch
            active_high: True si el switch es activo en alto, False si activo en bajo
            pull_up: True para habilitar pull-up interno
            bounce_time: Tiempo de debounce en segundos
        """
        self.pin = pin
        self.active_high = active_high
        self.logger = logging.getLogger("gate_system")
        self._lock = Lock()
        self._last_state = None
        self._state_change_callbacks = []
        
        try:
            self.switch = Button(
                pin=pin,
                pull_up=pull_up,
                bounce_time=bounce_time,
                hold_time=1
            )
            
            # # Configurar callbacks para cambios de estado
            # self.switch.when_pressed = self._on_state_change
            # self.switch.when_released = self._on_state_change
            
            self.logger.info(f"🔌 Reed Switch inicializado en GPIO {pin}")
            
        except Exception as e:
            self.logger.error(f"❌ Error inicializando Reed Switch: {e}")
            self.switch = None
    
    # def _on_state_change(self):
    #     """Callback interno para cambios de estado"""
    #     current_state = self.is_closed()
    #     if current_state != self._last_state:
    #         self._last_state = current_state
    #         state_text = "cerrado" if current_state else "abierto"
    #         self.logger.info(f"🚪 Estado del portón: {state_text}")
            
    #         # Ejecutar callbacks registrados
    #         for callback in self._state_change_callbacks:
    #             try:
    #                 callback(current_state)
    #             except Exception as e:
    #                 self.logger.error(f"❌ Error en callback de estado: {e}")
    
    def is_closed(self):
        """Verificar si el portón está cerrado"""
        if not self.switch:
            return None
        
        try:
            with self._lock:
                # Si active_high=False, el switch está cerrado cuando is_pressed=True
                # Si active_high=True, el switch está cerrado cuando is_pressed=False
                if self.active_high:
                    return not self.switch.is_pressed
                else:
                    return self.switch.is_pressed
        except Exception as e:
            self.logger.error(f"❌ Error leyendo estado del reed switch: {e}")
            return None
    
    def is_open(self):
        """Verificar si el portón está abierto"""
        closed_state = self.is_closed()
        return not closed_state if closed_state is not None else None
    
    def wait_for_closed(self, timeout=30):
        """Esperar hasta que el portón esté cerrado"""
        if not self.switch:
            return False
        
        try:
            start_time = time.time()
            while time.time() - start_time < timeout:
                if self.is_closed():
                    return True
                time.sleep(0.1)
            return False
        except Exception:
            return False
    
    def wait_for_open(self, timeout=30):
        """Esperar hasta que el portón esté abierto"""
        if not self.switch:
            return False
        
        try:
            start_time = time.time()
            while time.time() - start_time < timeout:
                if self.is_open():
                    return True
                time.sleep(0.1)
            return False
        except Exception:
            return False
    
    # def add_state_change_callback(self, callback):
    #     """Agregar callback para cambios de estado"""
    #     self._state_change_callbacks.append(callback)
    
    # def remove_state_change_callback(self, callback):
    #     """Remover callback de cambios de estado"""
    #     if callback in self._state_change_callbacks:
    #         self._state_change_callbacks.remove(callback)
    
    # def get_state_text(self):
    #     """Obtener estado como texto legible"""
    #     if self.is_closed():
    #         return "cerrado"
    #     elif self.is_open():
    #         return "abierto"
    #     else:
    #         return "desconocido"
    
    def cleanup(self):
        """Limpiar recursos del reed switch"""
        try:
            if self.switch:
                self.switch.close()
            self.logger.info("🔌 Reed Switch cerrado")
        except Exception as e:
            self.logger.error(f"❌ Error cerrando Reed Switch: {e}")