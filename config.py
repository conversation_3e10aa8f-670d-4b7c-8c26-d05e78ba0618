import os
from dataclasses import dataclass

@dataclass
class Config:
    # Security settings
    API_KEY: str = os.getenv("GATE_API_KEY", "dd975ff0f042e189131a14f8a32cfe5bb1bd66ad")
    SECRET_KEY: str = os.getenv("FLASK_SECRET_KEY", "your-secret-key-change-in-production")

    # Network settings
    FLASK_HOST: str = os.getenv("FLASK_HOST", "0.0.0.0")
    FLASK_PORT: int = int(os.getenv("FLASK_PORT", "8080"))
    DEBUG: bool = os.getenv("DEBUG", "False").lower() == "true"

    # Camera settings
    RTSP_CAMERA_ENTRADA: str = "rtsp://admin:admin@**************:554/11"
    RTSP_CAMERA_SALIDA: str = "rtsp://admin:admin@**************:554/11"

    # File paths
    SNAPSHOTS_DIR: str = "snapshots"
    LOGS_DIR: str = "logs"
    AUTHORIZED_PLATES_FILE: str = "authorized_license_plates.json"

    # Hardware settings
    RELAY_PIN: int = 17
    CYCLE_INTERVAL: int = 5

    # Pines para sensores JSN-SR04T
    PIN_SENSOR_ENTRADA_TRIGGER: int = 23
    PIN_SENSOR_ENTRADA_ECHO: int = 24
    PIN_SENSOR_SALIDA_TRIGGER: int = 25
    PIN_SENSOR_SALIDA_ECHO: int = 8
    PIN_SENSOR_PASADA_TRIGGER: int = 7
    PIN_SENSOR_PASADA_ECHO: int = 1

    # Public access settings
    ENABLE_PUBLIC_ACCESS: bool = os.getenv("ENABLE_PUBLIC_ACCESS", "False").lower() == "true"
    ALLOWED_IPS: list = os.getenv("ALLOWED_IPS", "").split(",") if os.getenv("ALLOWED_IPS") else []

config = Config()
