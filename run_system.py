import threading
import time
from gate_control_system import GateControlSystem
from web_server import app
from utils.logger import setup_logger

logger = setup_logger("gate_system")

def run_gate_system():
    try:
        system = GateControlSystem()
        system.run()
    except Exception as e:
        logger.error(f"❌ Error en sistema de portón: {e}")

def run_web_server():
    try:
        app.run(host='0.0.0.0', port=8080, debug=False, use_reloader=False)
    except Exception as e:
        logger.error(f"❌ Error en servidor web: {e}")

if __name__ == "__main__":
    logger.info("🚀 Iniciando sistema completo...")
    
    gate_thread = threading.Thread(target=run_gate_system, daemon=True)
    web_thread = threading.Thread(target=run_web_server, daemon=True)
    gate_thread.start()
    web_thread.start()
    time.sleep(2)
    
    logger.info("✅ Sistema iniciado")

    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("🛑 Deteniendo sistema...")
